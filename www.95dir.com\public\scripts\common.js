//搜索
$(document).ready(function(){
    $("#selopt").hover(
        function(){
            $("#options").slideDown();
            $("#options li a").click(function(){
                $("#cursel").text($(this).text());
                $("#type").attr("value", $(this).attr("name"));
                $("#options").hide();
            });
        },
        
        function(){$("#options").hide();}
    )   
})

//搜索伪静态
function rewrite_search(){
	var type = $("#type").val();
	var query = $.trim($("#query").val());
	if (type == null) {type = "tags"}
	if (query == "") {
		alert("\u8bf7\u8f93\u5165\u641c\u7d22\u5173\u952e\u5b57\uff01");
		$("#query").focus();
		return false;
	} else {
		if (rewrite == 1) {
			window.location.href = sitepath + "search-" + type + "-" + encodeURI(query) + ".html";
		} else if (rewrite == 2) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query) + ".html";
		} else if (rewrite == 3) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query);
		} else {
			this.form.submit();
		}
	}
	return false;
}

//验证url
function checkurl(url){
	if (url == '') {
		$("#msg").html('请输入网站域名！');
		return false;
	}
	
	$(document).ready(function(){$("#msg").html('<img src="' + sitepath + 'public/images/loading.gif" align="absmiddle"> 正在验证，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=check', data: 'url=' + url, cache: false, success: function(data){$("#msg").html(data)}});});
return true;
};

//获取META
function getmeta() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}
	$(document).ready(function(){$("#meta_btn").val('正在获取，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=crawl', data: 'url=' + url, datatype: "script", cache: false, success: function(data){$("body").append(data); $("#meta_btn").val('重新获取');}});});
}

//获取IP, PageRank, Sogou PageRank, Alexa
function getdata() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}

	// 显示加载状态
	$("#data_btn").val('正在获取，请稍候...').prop('disabled', true);

	$.ajax({
		type: "GET",
		url: sitepath + '?mod=ajaxget&type=data',
		data: 'url=' + encodeURIComponent(url),
		dataType: "html",
		cache: false,
		timeout: 120000, // 2分钟超时
		success: function(data) {
			console.log('获取数据成功:', data);
			$("body").append(data);
			// 注意：按钮状态由服务器端的JavaScript代码控制
		},
		error: function(xhr, status, error) {
			console.error('获取数据失败:', status, error);
			alert('获取数据失败，请稍后重试。错误信息：' + error);
			$("#data_btn").val('重新获取').prop('disabled', false);
		}
	});
}

//添加收藏
function addfav(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=addfav", data: "wid=" + wid, cache: false, success: function(data){$("body").append(data)}});});
};

//点出统计
function clickout(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=outstat", data: "wid=" + wid, cache: false, success: function(data){}});});
};

//错误报告
function report(obj, wid) {
	$(document).ready(function(){if (confirm("确认报告此错误吗？")){ $("#" + obj).html("正在提交，请稍候..."); $.ajax({type: "GET", url: sitepath + "?mod=getdata&type=error", data: "wid=" + wid, cache: false, success: function(data){$("#" + obj).html(data);}})};});
};

//验证码
function refreshimg(obj) {
	var randnum = Math.random();
	$("#" + obj).html('<img src="' + sitepath + 'source/include/captcha.php?s=' + randnum + '" align="absmiddle" alt="看不清楚?换一张" onclick="this.src+='+ randnum +'" style="cursor: pointer;">');
}

    // 显示打赏弹窗
    function showDonatePopup() {
        document.getElementById('donate-popup').style.display = 'flex';
    }

    // 关闭打赏弹窗
    function closeDonatePopup() {
        document.getElementById('donate-popup').style.display = 'none';
    }

    // 点击弹窗外部关闭
    window.onclick = function(event) {
        var popup = document.getElementById('donate-popup');
        if (event.target == popup) {
            popup.style.display = 'none';
        }
    }
    
// 推荐框颜色渐变    
    function generateColor(index) {
  // 使用 HSL 模式，根据索引生成不同的色调
  const hue = (index * 360) / 35; // 35 是你希望的颜色数量
  return `hsl(${hue}, 100%, 50%)`;
}

const items = document.querySelectorAll('#bestbox ul li');
items.forEach((item, index) => {
  item.style.backgroundColor = generateColor(index + 4); // +1 是因为索引从 0 开始
});

//首页轮播条
			function AutoScroll(obj){
                $(obj).find("ul:first").animate({
                        marginTop:"-22px"
                },500,function(){
                        $(this).css({marginTop:"0px"}).find("li:first").appendTo(this);
                });
			}
			$(document).ready(function(){
				timer=setInterval('AutoScroll(".site-notice")',4000);
			});
			$('#lunbo li').mousemove(function(){clearInterval(timer)});
			$('#lunbo li').mouseout(function(){timer=setInterval('AutoScroll(".site-notice")',5000)});
			

var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?de6784c7f19b11f9d9d70711252011fe";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();

// 打赏JS
function showDonatePopup() {
document.getElementById('donate-popup').style.display = 'flex';
}

function closeDonatePopup() {
document.getElementById('donate-popup').style.display = 'none';
}

// 选项卡切换功能
function initTabSwitching() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabItems.forEach(function(tabItem) {
        tabItem.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            tabItems.forEach(function(item) {
                item.classList.remove('active');
            });
            tabPanes.forEach(function(pane) {
                pane.classList.remove('active');
            });

            // 添加当前选中的活动状态
            this.classList.add('active');
            const targetPane = document.getElementById(targetTab + '-content');
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // 添加点击动画效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 数据归档月份切换功能
function initArchiveDataSwitching() {
    // 默认加载当前月份（7月）的数据
    if (typeof loadArchiveData === 'function') {
        loadArchiveData('2025', '07');
    }

    // 数据归档自动滚动控制变量
    let archiveAutoSwitchTimer = null;
    let currentArchiveIndex = 0;
    let isArchiveHovering = false;
    const archiveLinks = document.querySelectorAll('.archive-month-link');

    // 绑定月份链接点击事件
    archiveLinks.forEach(function(link, index) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const year = this.getAttribute('data-year');
            const month = this.getAttribute('data-month');

            // 更新活动状态
            archiveLinks.forEach(function(l) {
                l.classList.remove('active');
            });
            this.classList.add('active');

            // 更新当前索引
            currentArchiveIndex = index;

            // 加载对应月份的数据
            if (typeof loadArchiveData === 'function') {
                loadArchiveData(year, month);
            }

            // 重置自动切换
            resetArchiveAutoSwitch();
        });

        // 鼠标悬停时停止自动切换
        link.addEventListener('mouseenter', function() {
            isArchiveHovering = true;
            clearTimeout(archiveAutoSwitchTimer);
        });

        // 鼠标离开时恢复自动切换
        link.addEventListener('mouseleave', function() {
            isArchiveHovering = false;
            startArchiveAutoSwitch();
        });
    });

    // 启动自动切换
    startArchiveAutoSwitch();

    function startArchiveAutoSwitch() {
        if (isArchiveHovering) return;

        archiveAutoSwitchTimer = setTimeout(function() {
            if (!isArchiveHovering && archiveLinks.length > 1) {
                // 添加滚动效果
                const currentLink = archiveLinks[currentArchiveIndex];
                currentLink.classList.add('scrolling');

                // 切换到下一个月份
                currentArchiveIndex = (currentArchiveIndex + 1) % archiveLinks.length;
                const nextLink = archiveLinks[currentArchiveIndex];

                // 延迟一点时间显示滚动效果
                setTimeout(function() {
                    currentLink.classList.remove('scrolling');
                    nextLink.click();
                }, 300);
            }
        }, 5000); // 5秒自动切换
    }

    function resetArchiveAutoSwitch() {
        clearTimeout(archiveAutoSwitchTimer);
        startArchiveAutoSwitch();
    }
}

// 数据归档加载函数
function loadArchiveData(year, month) {
    const loadingEl = document.getElementById('archive-loading');
    const listEl = document.getElementById('archive-list');
    const errorEl = document.getElementById('archive-error');

    // 直接隐藏加载状态和错误状态，不显示加载过渡
    if (loadingEl) loadingEl.style.display = 'none';
    if (errorEl) errorEl.style.display = 'none';
    if (listEl) listEl.style.display = 'block';

    // 构造请求URL
    const url = '?mod=archives&date=' + year + month + '&ajax=1';

    // 发送AJAX请求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.websites) {
                displayWebsites(data.websites);
            } else {
                showArchiveError('暂无数据');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showArchiveError('加载失败，请稍后重试');
        });
}

function displayWebsites(websites) {
    const listEl = document.getElementById('archive-list');
    if (!listEl) return;

    if (websites.length === 0) {
        showArchiveError('该月份暂无收录网站');
        return;
    }

    // 限制只显示前12条数据
    const limitedWebsites = websites.slice(0, 12);

    let html = '';
    limitedWebsites.forEach(function(site) {
        // 提取域名用于favicon
        let domain = '';
        try {
            const url = new URL(site.web_url.startsWith('http') ? site.web_url : 'http://' + site.web_url);
            domain = url.hostname;
        } catch (e) {
            domain = site.web_url.replace(/^https?:\/\//, '').split('/')[0];
        }

        html += '<li>';
        html += '<span>' + site.web_ctime + '</span>';
        html += '<a href="' + site.web_link + '" title="' + site.web_name + ' - ' + site.web_url + '">';
        html += '<img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://' + domain + '" width="18" height="18" />';
        html += site.web_name + ' - <small>' + site.web_url + '</small>';
        // 如果是当天发表的网站，添加new图标
        if (site.is_today) {
            html += '<span class="new-icon">new</span>';
        }
        html += '</a>';
        html += '</li>';
    });

    listEl.innerHTML = html;
    listEl.style.display = 'block';
}

function showArchiveError(message) {
    const listEl = document.getElementById('archive-list');
    const errorEl = document.getElementById('archive-error');

    if (listEl) {
        listEl.innerHTML = '';
        listEl.style.display = 'none';
    }
    if (errorEl) {
        errorEl.textContent = message;
        errorEl.style.display = 'block';
    }
}

// 省份标签搜索功能
function initProvinceTagsSearch() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        tag.addEventListener('click', function(e) {
            e.preventDefault();

            // 添加点击动画效果
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 150);

            // 获取省份名称
            const province = this.textContent.trim();

            // 构造搜索URL
            const searchUrl = '?mod=search&keyword=' + encodeURIComponent(province);

            // 跳转到搜索页面
            window.location.href = searchUrl;
        });
    });
}

// 添加省份搜索提示
function addProvinceSearchTooltips() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        const province = tag.textContent.trim();
        tag.title = '点击搜索' + province + '地区的网站';
    });
}

// 弹窗相关功能
function initDonatePopup() {
    const donatePopup = document.getElementById('donate-popup');
    if (donatePopup) {
        donatePopup.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDonatePopup();
            }
        });
    }

    // ESC键关闭弹窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDonatePopup();
        }
    });
}

// 页面加载完成后初始化功能
$(document).ready(function() {
    console.log('Common.js v2.0 已加载 - 音乐播放器URL已隐藏');
    // 初始化选项卡切换
    if (typeof initTabSwitching === 'function') {
        initTabSwitching();
    }

    // 初始化数据归档切换
    if (document.querySelector('.archive-month-link')) {
        initArchiveDataSwitching();
    }

    // 初始化省份标签搜索功能
    if (document.querySelector('.province-tag')) {
        initProvinceTagsSearch();
        addProvinceSearchTooltips();
    }

    // 初始化弹窗功能
    initDonatePopup();

    // 初始化统计功能
    if (document.querySelector('.stat-card, .spider-card')) {
        initStatsFeatures();
    }

    // 初始化站点资讯分类切换功能
    initArticleSwitch();

    // 初始化省份标签搜索功能
    initProvinceTagsSearch();

    // 初始化音乐播放器
    initMusicPlayer();
});

// 统计功能相关
function updateNumberWithAnimation(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const oldValue = element.textContent;
    if (oldValue !== String(newValue) && oldValue !== '加载中...') {
        // 添加数字滚动效果
        animateNumber(element, parseInt(oldValue) || 0, newValue, 800);

        // 添加卡片闪烁效果
        const card = element.closest('.stat-card, .spider-card');
        if (card) {
            card.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.6)';
            setTimeout(() => {
                card.style.boxShadow = '';
            }, 1000);
        }
    } else if (oldValue === '加载中...') {
        element.textContent = newValue;
    }
}

// 数字滚动动画
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + difference * easeOutQuart);

        element.textContent = current;
        element.classList.add('stats-number', 'updated');

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            setTimeout(() => {
                element.classList.remove('updated');
            }, 300);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 更新今日访问统计和蜘蛛统计
function updateTodayStats() {
    fetch('?mod=datastats&ajax=1')
        .then(response => response.json())
        .then(data => {
            // 更新今日访问统计
            if (data.today_stats) {
                updateNumberWithAnimation('todayTotalVisits', data.today_stats.total_visits || 0);
                updateNumberWithAnimation('todaySiteVisits', data.today_stats.total_sites || 0);
                updateNumberWithAnimation('todayArticleVisits', data.today_stats.total_articles || 0);
                updateNumberWithAnimation('todayOutlinks', data.today_stats.total_outlinks || 0);
            }

            // 更新蜘蛛统计
            if (data.spider_stats) {
                updateNumberWithAnimation('spiderGoogle', data.spider_stats.google || 0);
                updateNumberWithAnimation('spiderBaidu', data.spider_stats.baidu || 0);
                updateNumberWithAnimation('spiderBing', data.spider_stats.bing || 0);
                updateNumberWithAnimation('spiderSogou', data.spider_stats.sogou || 0);
                updateNumberWithAnimation('spiderSo360', data.spider_stats.so360 || 0);
                updateNumberWithAnimation('spiderBytedance', data.spider_stats.bytedance || 0);
            }
        })
        .catch(() => {
            console.log('今日统计服务不可用');
            // 设置默认值
            const todayElements = ['todayTotalVisits', 'todaySiteVisits', 'todayArticleVisits', 'todayOutlinks'];
            const spiderElements = ['spiderGoogle', 'spiderBaidu', 'spiderBing', 'spiderSogou', 'spiderSo360', 'spiderBytedance'];

            todayElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '0';
            });

            spiderElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '0';
            });
        });
}

// 添加卡片点击效果
function addCardClickEffects() {
    document.querySelectorAll('.stat-card, .spider-card').forEach(card => {
        card.addEventListener('click', function() {
            // 点击波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// 初始化统计功能
function initStatsFeatures() {
    // 添加波纹动画CSS
    const rippleStyle = document.createElement('style');
    rippleStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(rippleStyle);

    // 立即更新统计数据
    updateTodayStats();
    addCardClickEffects();

    // 设置定时器 - 今日统计每5分钟更新
    setInterval(updateTodayStats, 300000);
}

// ==================== 首页专用功能 ====================

// 站点资讯分类切换功能
function initArticleSwitch() {
    let autoSwitchTimer = null;
    let currentCateIndex = 0;
    let isHovering = false;

    // 获取所有分类链接
    const cateLinks = document.querySelectorAll('.article-cate-link');

    console.log('文章分类链接数量:', cateLinks.length); // 调试信息

    if (cateLinks.length === 0) return;

    // 默认加载最新文章
    loadArticleData(0);

    // 绑定分类链接点击事件
    cateLinks.forEach(function(link, index) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const cateId = this.getAttribute('data-cate-id');

            // 更新活动状态
            cateLinks.forEach(function(l) {
                l.classList.remove('active');
            });
            this.classList.add('active');

            // 更新当前索引
            currentCateIndex = index;

            // 加载对应分类的文章
            loadArticleData(cateId);

            // 重置自动切换
            resetAutoSwitch();
        });

        // 鼠标悬停时停止自动切换
        link.addEventListener('mouseenter', function() {
            isHovering = true;
            clearTimeout(autoSwitchTimer);
        });

        // 鼠标离开时恢复自动切换
        link.addEventListener('mouseleave', function() {
            isHovering = false;
            startAutoSwitch();
        });
    });

    // 启动自动切换
    startAutoSwitch();

    function startAutoSwitch() {
        if (isHovering) return;

        autoSwitchTimer = setTimeout(function() {
            if (!isHovering && cateLinks.length > 1) {
                // 添加滚动效果
                const currentLink = cateLinks[currentCateIndex];
                console.log('添加滚动效果到:', currentLink.textContent); // 调试信息
                currentLink.classList.add('scrolling');

                // 切换到下一个分类
                currentCateIndex = (currentCateIndex + 1) % cateLinks.length;
                const nextLink = cateLinks[currentCateIndex];

                // 延迟一点时间显示滚动效果
                setTimeout(function() {
                    currentLink.classList.remove('scrolling');
                    console.log('点击下一个链接:', nextLink.textContent); // 调试信息
                    nextLink.click();
                }, 300);
            }
        }, 5000); // 5秒自动切换
    }

    function resetAutoSwitch() {
        clearTimeout(autoSwitchTimer);
        startAutoSwitch();
    }
}

function loadArticleData(cateId) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    if (!listEl || !errorEl) return;

    // 直接隐藏错误状态，显示列表
    errorEl.style.display = 'none';
    listEl.style.display = 'block';

    // 构造请求URL
    const url = '?mod=article&cate_id=' + cateId + '&ajax=1&limit=10';

    // 发送AJAX请求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.articles) {
                displayArticles(data.articles);
            } else {
                showArticleError('暂无文章');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showArticleError('加载失败，请稍后重试');
        });
}

function displayArticles(articles) {
    const listEl = document.getElementById('article-list');

    if (articles.length === 0) {
        showArticleError('该分类暂无文章');
        return;
    }

    let html = '';
    articles.forEach(function(article, index) {
        html += '<li data-number="' + (index + 1) + '">';
        html += '<span>' + article.art_ctime + '</span>';
        html += '<a href="' + article.art_link + '" title="' + article.art_title + '">';
        html += article.art_title;
        // 如果是当天发表的文章，添加new图标
        if (article.is_today) {
            html += '<span class="new-icon">new</span>';
        }
        html += '</a>';
        html += '</li>';
    });

    listEl.innerHTML = html;
    listEl.style.display = 'block';
}

function showArticleError(message) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    listEl.innerHTML = '';
    listEl.style.display = 'none';
    errorEl.textContent = message;
    errorEl.style.display = 'block';
}

// 省份标签搜索功能（首页专用版本）
function handleProvinceSearch(tagElement) {
    const province = tagElement.getAttribute('data-province');

    if (!province) return;

    // 添加点击动画效果
    tagElement.classList.add('clicked');
    setTimeout(function() {
        tagElement.classList.remove('clicked');
    }, 300);

    // 执行搜索
    performProvinceSearch(province);
}

function performProvinceSearch(province) {
    // 构建搜索URL
    const searchUrl = '?mod=search&type=name&query=' + encodeURIComponent(province);

    // 跳转到搜索结果页面
    window.location.href = searchUrl;
}

// 为省份标签添加搜索提示功能
function addProvinceSearchTooltips() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        const province = tag.getAttribute('data-province');
        if (province) {
            tag.title = '点击搜索' + province + '地区的网站';
        }
    });
}

// ==================== 音乐播放器功能 ====================

// 音乐播放器全局变量
let musicPlayer = {
    currentPlaylist: [],
    currentIndex: 0,
    audioElement: null,
    isPlaying: false,
    isMuted: false,
    volume: 0.5
};

// 初始化音乐播放器
function initMusicPlayer() {
    // 检查是否存在音乐播放器容器
    const musicContainer = document.getElementById('music-player-container');
    if (!musicContainer) {
        console.log('音乐播放器容器不存在，跳过初始化');
        return;
    }

    console.log('初始化音乐播放器...');

    // 创建隐藏的audio元素
    if (!musicPlayer.audioElement) {
        musicPlayer.audioElement = document.createElement('audio');
        musicPlayer.audioElement.volume = musicPlayer.volume;
        musicPlayer.audioElement.preload = 'none'; // 不预加载音频
        document.body.appendChild(musicPlayer.audioElement);
        console.log('创建音频元素完成');
    }

    // 绑定播放器控制事件
    bindMusicPlayerEvents();

    // 延迟加载音乐列表，确保DOM完全准备好
    setTimeout(() => {
        loadMusicList();
    }, 100);
}

// 绑定音乐播放器事件
function bindMusicPlayerEvents() {
    const playBtn = document.getElementById('playBtn');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const volumeBtn = document.getElementById('volumeBtn');
    const volumeSlider = document.getElementById('volumeSlider');
    const refreshBtn = document.getElementById('refreshList');

    if (playBtn) {
        playBtn.addEventListener('click', togglePlayPause);
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', playPrevious);
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', playNext);
    }

    if (volumeBtn) {
        volumeBtn.addEventListener('click', toggleMute);
    }

    if (volumeSlider) {
        volumeSlider.addEventListener('input', function() {
            setVolume(this.value / 100);
        });
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadMusicList();
        });
    }

    // 绑定audio元素事件
    if (musicPlayer.audioElement) {
        musicPlayer.audioElement.addEventListener('loadstart', onAudioLoadStart);
        musicPlayer.audioElement.addEventListener('canplay', onAudioCanPlay);
        musicPlayer.audioElement.addEventListener('play', onAudioPlay);
        musicPlayer.audioElement.addEventListener('pause', onAudioPause);
        musicPlayer.audioElement.addEventListener('ended', onAudioEnded);
        musicPlayer.audioElement.addEventListener('timeupdate', onAudioTimeUpdate);
        musicPlayer.audioElement.addEventListener('error', onAudioError);
    }
}

// 加载音乐列表
async function loadMusicList() {
    const musicList = document.getElementById('musicList');
    if (!musicList) {
        console.error('找不到musicList元素');
        return;
    }

    console.log('开始加载音乐列表...');

    // 显示加载状态
    musicList.innerHTML = '<li class="loading-item">正在加载音乐列表...</li>';

    try {
        console.log('发送API请求: ?mod=ajaxget&type=music_list');
        const response = await fetch('?mod=ajaxget&type=music_list');

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('音乐列表API响应:', data);

        if (data.success) {
            if (data.music_list && data.music_list.length > 0) {
                musicPlayer.currentPlaylist = data.music_list;
                renderMusicList();
                console.log('音乐列表加载成功，共', data.music_list.length, '首歌曲');
            } else {
                musicList.innerHTML = '<li class="error-item">暂无音乐数据</li>';
                console.log('API返回成功但没有音乐数据');
            }
        } else {
            const errorMsg = data.error || '未知错误';
            musicList.innerHTML = `<li class="error-item">加载失败: ${errorMsg}</li>`;
            console.error('API返回错误:', errorMsg);
        }
    } catch (error) {
        console.error('加载音乐列表失败:', error);
        musicList.innerHTML = `<li class="error-item">网络错误: ${error.message}</li>`;
    }
}

// 渲染音乐列表 - 版本2.0 (隐藏URL)
function renderMusicList() {
    const musicList = document.getElementById('musicList');
    if (!musicList) {
        console.error('找不到musicList元素');
        return;
    }

    if (musicPlayer.currentPlaylist.length === 0) {
        musicList.innerHTML = '<li class="error-item">播放列表为空</li>';
        return;
    }

    console.log('渲染音乐列表 v2.0 (隐藏URL)，共', musicPlayer.currentPlaylist.length, '首歌曲');

    const html = musicPlayer.currentPlaylist.map((track, index) => {
        // 安全地处理track数据 - 注意：不显示URL
        const title = track.title || '未知歌曲';
        const isActive = index === musicPlayer.currentIndex;

        // 调试：确认不包含URL
        console.log(`渲染歌曲 ${index}: ${title} (URL已隐藏)`);

        return `
            <li onclick="playTrack(${index})" class="music-item ${isActive ? 'active' : ''}" title="点击播放: ${title}">
                <div class="track-info">
                    <div class="track-title">${title}</div>
                    <!-- URL已被隐藏，不显示 track.url -->
                </div>
                <div class="track-controls" style="float: right;">
                    <i class="fas ${isActive && musicPlayer.isPlaying ? 'fa-pause' : 'fa-play'}"></i>
                </div>
                <div style="clear: both;"></div>
            </li>
        `;
    }).join('');

    musicList.innerHTML = html;
    console.log('音乐列表渲染完成');
}

// 播放指定曲目
function playTrack(index) {
    if (index < 0 || index >= musicPlayer.currentPlaylist.length) return;

    console.log('播放曲目:', index, musicPlayer.currentPlaylist[index]);

    musicPlayer.currentIndex = index;
    const track = musicPlayer.currentPlaylist[index];

    // 处理音频URL
    const audioUrl = processAudioUrl(track.url);
    console.log('处理后的音频URL:', audioUrl);

    // 设置音频源
    musicPlayer.audioElement.src = audioUrl;

    // 更新当前播放信息
    updateCurrentTrackInfo(track);

    // 更新列表显示
    renderMusicList();

    // 尝试播放
    musicPlayer.audioElement.play().catch(error => {
        console.error('播放失败:', error);
        updateCurrentTrackInfo({title: '播放失败: ' + error.message});
    });
}

// 处理音频URL
function processAudioUrl(url) {
    // 处理网易云音乐链接
    if (url.includes('music.163.com')) {
        const match = url.match(/id=(\d+)/);
        if (match) {
            return `https://music.163.com/song/media/outer/url?id=${match[1]}`;
        }
    }

    // 对于直接的音频文件链接，直接返回
    if (url.match(/\.(mp3|m4a|wav|flac|aac|ogg)(\?.*)?$/i)) {
        return url;
    }

    // 默认返回原链接
    return url;
}

// 切换播放/暂停
function togglePlayPause() {
    if (!musicPlayer.audioElement || !musicPlayer.audioElement.src) {
        // 如果没有音频源，播放第一首歌
        if (musicPlayer.currentPlaylist.length > 0) {
            playTrack(0);
        }
        return;
    }

    if (musicPlayer.isPlaying) {
        musicPlayer.audioElement.pause();
    } else {
        musicPlayer.audioElement.play().catch(error => {
            console.error('播放失败:', error);
        });
    }
}

// 播放上一首
function playPrevious() {
    if (musicPlayer.currentPlaylist.length === 0) return;

    const prevIndex = musicPlayer.currentIndex > 0
        ? musicPlayer.currentIndex - 1
        : musicPlayer.currentPlaylist.length - 1;

    playTrack(prevIndex);
}

// 播放下一首
function playNext() {
    if (musicPlayer.currentPlaylist.length === 0) return;

    const nextIndex = musicPlayer.currentIndex < musicPlayer.currentPlaylist.length - 1
        ? musicPlayer.currentIndex + 1
        : 0;

    playTrack(nextIndex);
}

// 切换静音
function toggleMute() {
    if (!musicPlayer.audioElement) return;

    musicPlayer.isMuted = !musicPlayer.isMuted;
    musicPlayer.audioElement.muted = musicPlayer.isMuted;

    const volumeBtn = document.getElementById('volumeBtn');
    if (volumeBtn) {
        const icon = volumeBtn.querySelector('i');
        if (icon) {
            icon.className = musicPlayer.isMuted ? 'fas fa-volume-mute' : 'fas fa-volume-up';
        }
    }
}

// 设置音量
function setVolume(volume) {
    if (!musicPlayer.audioElement) return;

    musicPlayer.volume = Math.max(0, Math.min(1, volume));
    musicPlayer.audioElement.volume = musicPlayer.volume;

    // 更新静音状态
    if (musicPlayer.volume === 0) {
        musicPlayer.isMuted = true;
        musicPlayer.audioElement.muted = true;
    } else if (musicPlayer.isMuted) {
        musicPlayer.isMuted = false;
        musicPlayer.audioElement.muted = false;
    }

    // 更新音量按钮图标
    const volumeBtn = document.getElementById('volumeBtn');
    if (volumeBtn) {
        const icon = volumeBtn.querySelector('i');
        if (icon) {
            if (musicPlayer.volume === 0 || musicPlayer.isMuted) {
                icon.className = 'fas fa-volume-mute';
            } else if (musicPlayer.volume < 0.5) {
                icon.className = 'fas fa-volume-down';
            } else {
                icon.className = 'fas fa-volume-up';
            }
        }
    }
}

// 更新当前播放信息
function updateCurrentTrackInfo(track) {
    const currentTitle = document.getElementById('currentTitle');
    if (currentTitle) {
        currentTitle.textContent = track.title || '未知歌曲';
    }
}

// 音频事件处理函数
function onAudioLoadStart() {
    console.log('开始加载音频...');
    updateCurrentTrackInfo({title: '正在加载...'});
}

function onAudioCanPlay() {
    console.log('音频可以播放');
}

function onAudioPlay() {
    console.log('音频开始播放');
    musicPlayer.isPlaying = true;

    // 更新播放按钮
    const playBtn = document.getElementById('playBtn');
    if (playBtn) {
        const icon = playBtn.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-pause';
        }
    }
}

function onAudioPause() {
    console.log('音频暂停');
    musicPlayer.isPlaying = false;

    // 更新播放按钮
    const playBtn = document.getElementById('playBtn');
    if (playBtn) {
        const icon = playBtn.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-play';
        }
    }
}

function onAudioEnded() {
    console.log('音频播放结束');
    musicPlayer.isPlaying = false;

    // 自动播放下一首
    playNext();
}

function onAudioTimeUpdate() {
    if (!musicPlayer.audioElement) return;

    const currentTime = musicPlayer.audioElement.currentTime;
    const duration = musicPlayer.audioElement.duration;

    // 更新进度条
    const progressFill = document.getElementById('progressFill');
    if (progressFill && duration > 0) {
        const progress = (currentTime / duration) * 100;
        progressFill.style.width = progress + '%';
    }

    // 更新时间显示
    const currentTimeEl = document.getElementById('currentTime');
    const totalTimeEl = document.getElementById('totalTime');

    if (currentTimeEl) {
        currentTimeEl.textContent = formatTime(currentTime);
    }

    if (totalTimeEl && duration > 0) {
        totalTimeEl.textContent = formatTime(duration);
    }
}

function onAudioError(event) {
    console.error('音频播放错误:', event);
    const error = musicPlayer.audioElement.error;
    let errorMessage = '播放失败';

    if (error) {
        switch (error.code) {
            case error.MEDIA_ERR_ABORTED:
                errorMessage = '播放被中止';
                break;
            case error.MEDIA_ERR_NETWORK:
                errorMessage = '网络错误';
                break;
            case error.MEDIA_ERR_DECODE:
                errorMessage = '解码错误';
                break;
            case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                errorMessage = '音频格式不支持';
                break;
            default:
                errorMessage = '未知错误';
        }
    }

    updateCurrentTrackInfo({title: errorMessage});

    // 尝试播放下一首
    setTimeout(() => {
        playNext();
    }, 2000);
}

// 格式化时间显示
function formatTime(seconds) {
    if (isNaN(seconds) || seconds < 0) return '0:00';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    return minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds;
}

// 将音乐播放器函数暴露到全局作用域，供HTML onclick事件使用
window.playTrack = playTrack;



